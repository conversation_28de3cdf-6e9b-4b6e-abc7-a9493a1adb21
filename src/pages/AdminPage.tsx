import { useQuery } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { LoadingSpinner, ErrorMessage } from '../components/ui/StatusMessages';

interface Energieausweis {
  id: string;
  created_at: string | null; // Allow null for created_at
  certificate_type: string | null; // Allow null for certificate_type
  payment_status: string | null; // Allow null for payment_status
  order_number: string | null;
  // Add other fields as needed for display or export
}

export const AdminPage = () => {
  const { data: energieausweise, isLoading, error } = useQuery<Energieausweis[] | undefined>({
    queryKey: ['adminEnergieausweise'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('energieausweise')
        .select('id, created_at, certificate_type, payment_status, order_number'); // Select order_number

      if (error) throw error;
      return data || [];
    },
  });

  const exportToCsv = () => {
    if (!energieausweise || energieausweise.length === 0) {
      alert('Keine Daten zum Exportieren vorhanden.');
      return;
    }

    const headers = [
      'ID',
      'Bestellnummer', // Add order_number header
      'Erstellt Am',
      'Zertifikatstyp',
      'Zahlungsstatus',
    ];

    const rows = energieausweise.map(ea => [
      ea.id,
      ea.order_number || '', // Use order_number
      ea.created_at ? new Date(ea.created_at).toLocaleDateString('de-DE') : '',
      ea.certificate_type,
      ea.payment_status,
    ]);

    const csvContent = [
      headers.join(';'), // Semicolon separator
      ...rows.map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(';')) // Quote fields and handle internal quotes
    ].join('\n');

    const blob = new Blob([new Uint8Array([0xEF, 0xBB, 0xBF]), csvContent], { type: 'text/csv;charset=utf-8;' }); // UTF-8 with BOM
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `energieausweise_${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto py-8">
        <LoadingSpinner message="Lade Energieausweise..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto py-8">
        <ErrorMessage message={`Fehler beim Laden der Energieausweise: ${error.message}`} />
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Admin-Dashboard</h1>

      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-gray-800">Erfasste Energieausweise</h2>
        <button
          onClick={exportToCsv}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          CSV Exportieren
        </button>
      </div>

      {energieausweise && energieausweise.length > 0 ? (
        <div className="overflow-x-auto shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                  ID
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Bestellnummer
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Erstellt Am
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Zertifikatstyp
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Zahlungsstatus
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {energieausweise.map((ea) => (
                <tr key={ea.id}>
                  <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                    {ea.id}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.order_number || 'N/A'}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.created_at ? new Date(ea.created_at).toLocaleDateString('de-DE') : 'N/A'}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.certificate_type}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.payment_status}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="border border-gray-200 rounded-md p-4 text-center text-gray-500">
          Keine Energieausweise gefunden.
        </div>
      )}
    </div>
  );
};

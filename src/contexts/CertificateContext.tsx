import { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import { supabase } from '../lib/supabase';

// Define the context type
interface CertificateContextType {
  activeCertificateId: string | null;
  setActiveCertificateId: (id: string | null) => void;
  createNewCertificate: (certificateType: string) => Promise<string>;
  getCertificateById: (id: string) => Promise<any>;
  loading: boolean;
}

// Create the context with default values
const CertificateContext = createContext<CertificateContextType>({
  activeCertificateId: null,
  setActiveCertificateId: () => {},
  createNewCertificate: async () => '',
  getCertificateById: async () => null,
  loading: true,
});

// Create a provider component
export const CertificateProvider = ({ children }: { children: ReactNode }) => {
  const [activeCertificateId, setActiveCertificateId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // Load the active certificate ID from localStorage on mount
  useEffect(() => {
    const storedId = localStorage.getItem('activeCertificateId');
    if (storedId) {
      setActiveCertificateId(storedId);
    }
    setLoading(false);
  }, []);

  // Update localStorage when the active certificate ID changes
  useEffect(() => {
    if (activeCertificateId) {
      localStorage.setItem('activeCertificateId', activeCertificateId);
    } else {
      localStorage.removeItem('activeCertificateId');
    }
  }, [activeCertificateId]);

  // Function to create a new certificate
  const createNewCertificate = async (certificateType: string): Promise<string> => {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Nicht eingeloggt');

      // Create a new certificate record
      const { data, error } = await supabase
        .from('energieausweise')
        .insert({
          user_id: user.user.id,
          certificate_type: certificateType,
          status: 'in_progress',
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;
      
      // Set the new certificate as active
      if (data && data.id) {
        const certificateId = data.id;
        const orderNumber = `EA-${certificateId.slice(-8).toUpperCase()}`;

        const { error: updateError } = await supabase
          .from('energieausweise')
          .update({ order_number: orderNumber })
          .eq('id', certificateId);

        if (updateError) throw updateError;

        setActiveCertificateId(certificateId);
        return certificateId;
      }
      
      throw new Error('Fehler beim Erstellen des Zertifikats');
    } catch (error) {
      console.error('Error creating certificate:', error);
      throw error;
    }
  };

  // Function to get a certificate by ID
  const getCertificateById = async (id: string) => {
    try {
      const { data, error } = await supabase
        .from('energieausweise')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching certificate:', error);
      return null;
    }
  };

  // Create the context value object
  const contextValue: CertificateContextType = {
    activeCertificateId,
    setActiveCertificateId,
    createNewCertificate,
    getCertificateById,
    loading,
  };

  // Provide the context to children
  return (
    <CertificateContext.Provider value={contextValue}>
      {children}
    </CertificateContext.Provider>
  );
};

// Create a custom hook to use the certificate context
export const useCertificate = () => useContext(CertificateContext);
